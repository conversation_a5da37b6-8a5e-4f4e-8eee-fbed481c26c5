import type { IExecuteFunctions } from 'n8n-core';
import type { IDataObject, INodeType, INodeTypeDescription } from 'n8n-workflow';

export class LinearFetchProjects implements INodeType {
  description: INodeTypeDescription = {
    displayName: 'Linear → Get Projects',
    name: 'linearFetchProjects',
    group: ['transform'],
    version: 1,
    description: 'Retrieve every project in Linear',
    defaults: { name: 'Linear: Get Projects' },
    inputs: ['main'],
    outputs: ['main'],
    credentials: [{ name: 'httpHeaderAuth', required: true }],
    properties: [
      {
        displayName: 'Page Size',
        name: 'pageSize',
        type: 'number',
        default: 250,
        description: 'How many projects to fetch per request (1-250)',
      },
    ],
    // Let n8n AI Agent treat it as a “tool” if you like:
    usableAsTool: true,
  };

  async execute(this: IExecuteFunctions) {
    const token = this.getCredentials('httpHeaderAuth')?.headerValue as string;
    const pageSize = this.getNodeParameter('pageSize', 0) as number;

    let after: string | null = null;
    const allProjects: IDataObject[] = [];

    do {
      const body = {
        query: `query ($first:Int!,$after:String){
          projects(first:$first,after:$after){
            nodes{ id name state targetDate updatedAt }
            pageInfo{ hasNextPage endCursor }
          }}`,
        variables: { first: pageSize, after },
      };

      const { data } = await this.helpers.request!({
        method: 'POST',
        url: 'https://api.linear.app/graphql',
        headers: { Authorization: token },
        body,
        json: true,
      });

      allProjects.push(...data.projects.nodes);
      after = data.projects.pageInfo.hasNextPage
        ? data.projects.pageInfo.endCursor
        : null;
    } while (after);

    return [this.helpers.returnJsonArray(allProjects)];
  }
}
