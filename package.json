{"name": "n8n-nodes-linear-projects", "version": "0.1.0", "description": "n8n node to fetch all projects from Linear API", "keywords": ["n8n-community-node-package"], "license": "MIT", "homepage": "https://n8n.io", "author": {"name": "Your Name", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "git+https://github.com/yourusername/n8n-nodes-linear.git"}, "main": "index.js", "scripts": {"build": "tsc && gulp build:icons", "dev": "tsc --watch", "format": "prettier nodes credentials --write", "lint": "eslint nodes credentials package.json", "lintfix": "eslint nodes credentials package.json --fix", "prepublishOnly": "npm run build && npm run lint -s"}, "files": ["dist"], "n8n": {"n8nNodesApiVersion": 1, "credentials": ["dist/credentials/LinearApi.credentials.js"], "nodes": ["dist/nodes/FetchAllProjectsLinear/FetchAllProjectsLinear.node.js"]}, "devDependencies": {"@typescript-eslint/parser": "~5.45.0", "eslint-plugin-n8n-nodes-base": "~1.11.0", "gulp": "^4.0.2", "n8n-workflow": "*", "prettier": "^2.7.1", "typescript": "~4.8.4"}, "peerDependencies": {"n8n-workflow": "*"}}