import {
	IAuthenticateGeneric,
	ICredentialTestRequest,
	ICredentialType,
	INodeProperties,
} from 'n8n-workflow';

export class LinearApi implements ICredentialType {
	name = 'linearApi';
	displayName = 'Linear API';
	documentationUrl = 'https://developers.linear.app/docs/graphql/working-with-the-graphql-api';
	properties: INodeProperties[] = [
		{
			displayName: 'API Key',
			name: 'apiKey',
			type: 'string',
			typeOptions: { password: true },
			default: '',
			description: 'Your Linear API key. You can find this in your Linear settings under API.',
		},
	];

	authenticate: IAuthenticateGeneric = {
		type: 'generic',
		properties: {
			headers: {
				Authorization: '={{$credentials.apiKey}}',
			},
		},
	};

	test: ICredentialTestRequest = {
		request: {
			baseURL: 'https://api.linear.app',
			url: '/graphql',
			method: 'POST',
			body: {
				query: 'query { viewer { id name } }',
			},
		},
	};
}
