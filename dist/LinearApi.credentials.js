"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LinearApi = void 0;
class LinearApi {
    constructor() {
        this.name = 'linearApi';
        this.displayName = 'Linear API';
        this.documentationUrl = 'https://developers.linear.app/docs/graphql/working-with-the-graphql-api';
        this.properties = [
            {
                displayName: 'API Key',
                name: 'api<PERSON>ey',
                type: 'string',
                typeOptions: { password: true },
                default: '',
                description: 'Your Linear API key. You can find this in your Linear settings under API.',
            },
        ];
        this.authenticate = {
            type: 'generic',
            properties: {
                headers: {
                    Authorization: '={{$credentials.apiKey}}',
                },
            },
        };
        this.test = {
            request: {
                baseURL: 'https://api.linear.app',
                url: '/graphql',
                method: 'POST',
                body: {
                    query: 'query { viewer { id name } }',
                },
            },
        };
    }
}
exports.LinearApi = LinearApi;
